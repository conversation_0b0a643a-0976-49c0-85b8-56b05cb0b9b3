{"version": 3, "sources": ["../../use-sync-external-store/cjs/use-sync-external-store-shim.development.js", "../../use-sync-external-store/shim/index.js", "../../regexparam/dist/index.mjs", "../../wouter/esm/react-deps.js", "../../wouter/esm/use-browser-location.js", "../../wouter/esm/index.js"], "sourcesContent": ["/**\n * @license React\n * use-sync-external-store-shim.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function is(x, y) {\n      return (x === y && (0 !== x || 1 / x === 1 / y)) || (x !== x && y !== y);\n    }\n    function useSyncExternalStore$2(subscribe, getSnapshot) {\n      didWarnOld18Alpha ||\n        void 0 === React.startTransition ||\n        ((didWarnOld18Alpha = !0),\n        console.error(\n          \"You are using an outdated, pre-release alpha of React 18 that does not support useSyncExternalStore. The use-sync-external-store shim will not work correctly. Upgrade to a newer pre-release.\"\n        ));\n      var value = getSnapshot();\n      if (!didWarnUncachedGetSnapshot) {\n        var cachedValue = getSnapshot();\n        objectIs(value, cachedValue) ||\n          (console.error(\n            \"The result of getSnapshot should be cached to avoid an infinite loop\"\n          ),\n          (didWarnUncachedGetSnapshot = !0));\n      }\n      cachedValue = useState({\n        inst: { value: value, getSnapshot: getSnapshot }\n      });\n      var inst = cachedValue[0].inst,\n        forceUpdate = cachedValue[1];\n      useLayoutEffect(\n        function () {\n          inst.value = value;\n          inst.getSnapshot = getSnapshot;\n          checkIfSnapshotChanged(inst) && forceUpdate({ inst: inst });\n        },\n        [subscribe, value, getSnapshot]\n      );\n      useEffect(\n        function () {\n          checkIfSnapshotChanged(inst) && forceUpdate({ inst: inst });\n          return subscribe(function () {\n            checkIfSnapshotChanged(inst) && forceUpdate({ inst: inst });\n          });\n        },\n        [subscribe]\n      );\n      useDebugValue(value);\n      return value;\n    }\n    function checkIfSnapshotChanged(inst) {\n      var latestGetSnapshot = inst.getSnapshot;\n      inst = inst.value;\n      try {\n        var nextValue = latestGetSnapshot();\n        return !objectIs(inst, nextValue);\n      } catch (error) {\n        return !0;\n      }\n    }\n    function useSyncExternalStore$1(subscribe, getSnapshot) {\n      return getSnapshot();\n    }\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&\n      \"function\" ===\n        typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart &&\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(Error());\n    var React = require(\"react\"),\n      objectIs = \"function\" === typeof Object.is ? Object.is : is,\n      useState = React.useState,\n      useEffect = React.useEffect,\n      useLayoutEffect = React.useLayoutEffect,\n      useDebugValue = React.useDebugValue,\n      didWarnOld18Alpha = !1,\n      didWarnUncachedGetSnapshot = !1,\n      shim =\n        \"undefined\" === typeof window ||\n        \"undefined\" === typeof window.document ||\n        \"undefined\" === typeof window.document.createElement\n          ? useSyncExternalStore$1\n          : useSyncExternalStore$2;\n    exports.useSyncExternalStore =\n      void 0 !== React.useSyncExternalStore ? React.useSyncExternalStore : shim;\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&\n      \"function\" ===\n        typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop &&\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(Error());\n  })();\n", "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('../cjs/use-sync-external-store-shim.production.js');\n} else {\n  module.exports = require('../cjs/use-sync-external-store-shim.development.js');\n}\n", "/**\n * @param {string|RegExp} input The route pattern\n * @param {boolean} [loose] Allow open-ended matching. Ignored with `RegExp` input.\n */\nexport function parse(input, loose) {\n\tif (input instanceof RegExp) return { keys:false, pattern:input };\n\tvar c, o, tmp, ext, keys=[], pattern='', arr = input.split('/');\n\tarr[0] || arr.shift();\n\n\twhile (tmp = arr.shift()) {\n\t\tc = tmp[0];\n\t\tif (c === '*') {\n\t\t\tkeys.push(c);\n\t\t\tpattern += tmp[1] === '?' ? '(?:/(.*))?' : '/(.*)';\n\t\t} else if (c === ':') {\n\t\t\to = tmp.indexOf('?', 1);\n\t\t\text = tmp.indexOf('.', 1);\n\t\t\tkeys.push( tmp.substring(1, !!~o ? o : !!~ext ? ext : tmp.length) );\n\t\t\tpattern += !!~o && !~ext ? '(?:/([^/]+?))?' : '/([^/]+?)';\n\t\t\tif (!!~ext) pattern += (!!~o ? '?' : '') + '\\\\' + tmp.substring(ext);\n\t\t} else {\n\t\t\tpattern += '/' + tmp;\n\t\t}\n\t}\n\n\treturn {\n\t\tkeys: keys,\n\t\tpattern: new RegExp('^' + pattern + (loose ? '(?=$|\\/)' : '\\/?$'), 'i')\n\t};\n}\n\nvar RGX = /(\\/|^)([:*][^/]*?)(\\?)?(?=[/.]|$)/g;\n\n// error if key missing?\nexport function inject(route, values) {\n\treturn route.replace(RGX, (x, lead, key, optional) => {\n\t\tx = values[key=='*' ? key : key.substring(1)];\n\t\treturn x ? '/'+x : (optional || key=='*') ? '' : '/' + key;\n\t});\n}\n", "import * as React from 'react';\nexport { Fragment, cloneElement, createContext, createElement, forwardRef, isValidElement, useContext, useMemo, useRef, useState } from 'react';\nexport { useSyncExternalStore } from 'use-sync-external-store/shim/index.js';\n\n// React.useInsertionEffect is not available in React <18\n// This hack fixes a transpilation issue on some apps\nconst useBuiltinInsertionEffect = React[\"useInsertion\" + \"Effect\"];\n\n// Copied from:\n// https://github.com/facebook/react/blob/main/packages/shared/ExecutionEnvironment.js\nconst canUseDOM = !!(\n  typeof window !== \"undefined\" &&\n  typeof window.document !== \"undefined\" &&\n  typeof window.document.createElement !== \"undefined\"\n);\n\n// Copied from:\n// https://github.com/reduxjs/react-redux/blob/master/src/utils/useIsomorphicLayoutEffect.ts\n// \"<PERSON>act currently throws a warning when using useLayoutEffect on the server.\n// To get around it, we can conditionally useEffect on the server (no-op) and\n// useLayoutEffect in the browser.\"\nconst useIsomorphicLayoutEffect = canUseDOM\n  ? React.useLayoutEffect\n  : React.useEffect;\n\n// useInsertionEffect is already a noop on the server.\n// See: https://github.com/facebook/react/blob/main/packages/react-server/src/ReactFizzHooks.js\nconst useInsertionEffect =\n  useBuiltinInsertionEffect || useIsomorphicLayoutEffect;\n\n// Userland polyfill while we wait for the forthcoming\n// https://github.com/reactjs/rfcs/blob/useevent/text/0000-useevent.md\n// Note: \"A high-fidelity polyfill for useEvent is not possible because\n// there is no lifecycle or Hook in React that we can use to switch\n// .current at the right timing.\"\n// So we will have to make do with this \"close enough\" approach for now.\nconst useEvent = (fn) => {\n  const ref = React.useRef([fn, (...args) => ref[0](...args)]).current;\n  // Per Dan Abramov: useInsertionEffect executes marginally closer to the\n  // correct timing for ref synchronization than useLayoutEffect on React 18.\n  // See: https://github.com/facebook/react/pull/25881#issuecomment-1356244360\n  useInsertionEffect(() => {\n    ref[0] = fn;\n  });\n  return ref[1];\n};\n\nexport { useEvent, useInsertionEffect, useIsomorphicLayoutEffect };\n", "import { useSyncExternalStore } from './react-deps.js';\n\n/**\n * History API docs @see https://developer.mozilla.org/en-US/docs/Web/API/History\n */\nconst eventPopstate = \"popstate\";\nconst eventPushState = \"pushState\";\nconst eventReplaceState = \"replaceState\";\nconst eventHashchange = \"hashchange\";\nconst events = [\n  eventPopstate,\n  eventPushState,\n  eventReplaceState,\n  eventHashchange,\n];\n\nconst subscribeToLocationUpdates = (callback) => {\n  for (const event of events) {\n    addEventListener(event, callback);\n  }\n  return () => {\n    for (const event of events) {\n      removeEventListener(event, callback);\n    }\n  };\n};\n\nconst useLocationProperty = (fn, ssrFn) =>\n  useSyncExternalStore(subscribeToLocationUpdates, fn, ssrFn);\n\nconst currentSearch = () => location.search;\n\nconst useSearch = ({ ssrSearch = \"\" } = {}) =>\n  useLocationProperty(currentSearch, () => ssrSearch);\n\nconst currentPathname = () => location.pathname;\n\nconst usePathname = ({ ssrPath } = {}) =>\n  useLocationProperty(\n    currentPathname,\n    ssrPath ? () => ssrPath : currentPathname\n  );\n\nconst currentHistoryState = () => history.state;\nconst useHistoryState = () =>\n  useLocationProperty(currentHistoryState, () => null);\n\nconst navigate = (to, { replace = false, state = null } = {}) =>\n  history[replace ? eventReplaceState : eventPushState](state, \"\", to);\n\n// the 2nd argument of the `useBrowserLocation` return value is a function\n// that allows to perform a navigation.\nconst useBrowserLocation = (opts = {}) => [usePathname(opts), navigate];\n\nconst patchKey = Symbol.for(\"wouter_v3\");\n\n// While History API does have `popstate` event, the only\n// proper way to listen to changes via `push/replaceState`\n// is to monkey-patch these methods.\n//\n// See https://stackoverflow.com/a/4585031\nif (typeof history !== \"undefined\" && typeof window[patchKey] === \"undefined\") {\n  for (const type of [eventPushState, eventReplaceState]) {\n    const original = history[type];\n    // TODO: we should be using unstable_batchedUpdates to avoid multiple re-renders,\n    // however that will require an additional peer dependency on react-dom.\n    // See: https://github.com/reactwg/react-18/discussions/86#discussioncomment-1567149\n    history[type] = function () {\n      const result = original.apply(this, arguments);\n      const event = new Event(type);\n      event.arguments = arguments;\n\n      dispatchEvent(event);\n      return result;\n    };\n  }\n\n  // patch history object only once\n  // See: https://github.com/molefrog/wouter/issues/167\n  Object.defineProperty(window, patchKey, { value: true });\n}\n\nexport { navigate, useBrowserLocation, useHistoryState, useLocationProperty, usePathname, useSearch };\n", "import { parse } from 'regexparam';\nimport { useBrowserLocation, useSearch as useSearch$1 } from './use-browser-location.js';\nimport { createContext, forwardRef, useEvent, isValidElement, cloneElement, createElement, useContext, useRef, useMemo, useIsomorphicLayoutEffect, Fragment } from './react-deps.js';\n\n/*\n * Transforms `path` into its relative `base` version\n * If base isn't part of the path provided returns absolute path e.g. `~/app`\n */\nconst _relativePath = (base, path) =>\n  !path.toLowerCase().indexOf(base.toLowerCase())\n    ? path.slice(base.length) || \"/\"\n    : \"~\" + path;\n\n/**\n * When basepath is `undefined` or '/' it is ignored (we assume it's empty string)\n */\nconst baseDefaults = (base = \"\") => (base === \"/\" ? \"\" : base);\n\nconst absolutePath = (to, base) =>\n  to[0] === \"~\" ? to.slice(1) : baseDefaults(base) + to;\n\nconst relativePath = (base = \"\", path) =>\n  _relativePath(unescape(baseDefaults(base)), unescape(path));\n\n/*\n * Removes leading question mark\n */\nconst stripQm = (str) => (str[0] === \"?\" ? str.slice(1) : str);\n\n/*\n * decodes escape sequences such as %20\n */\nconst unescape = (str) => {\n  try {\n    return decodeURI(str);\n  } catch (_e) {\n    // fail-safe mode: if string can't be decoded do nothing\n    return str;\n  }\n};\n\nconst sanitizeSearch = (search) => unescape(stripQm(search));\n\n/*\n * Router and router context. Router is a lightweight object that represents the current\n * routing options: how location is managed, base path etc.\n *\n * There is a default router present for most of the use cases, however it can be overridden\n * via the <Router /> component.\n */\n\nconst defaultRouter = {\n  hook: useBrowserLocation,\n  searchHook: useSearch$1,\n  parser: parse,\n  base: \"\",\n  // this option is used to override the current location during SSR\n  ssrPath: undefined,\n  ssrSearch: undefined,\n  // optional context to track render state during SSR\n  ssrContext: undefined,\n  // customizes how `href` props are transformed for <Link />\n  hrefs: (x) => x,\n};\n\nconst RouterCtx = createContext(defaultRouter);\n\n// gets the closest parent router from the context\nconst useRouter = () => useContext(RouterCtx);\n\n/**\n * Parameters context. Used by `useParams()` to get the\n * matched params from the innermost `Route` component.\n */\n\nconst Params0 = {},\n  ParamsCtx = createContext(Params0);\n\nconst useParams = () => useContext(ParamsCtx);\n\n/*\n * Part 1, Hooks API: useRoute and useLocation\n */\n\n// Internal version of useLocation to avoid redundant useRouter calls\n\nconst useLocationFromRouter = (router) => {\n  const [location, navigate] = router.hook(router);\n\n  // the function reference should stay the same between re-renders, so that\n  // it can be passed down as an element prop without any performance concerns.\n  // (This is achieved via `useEvent`.)\n  return [\n    relativePath(router.base, location),\n    useEvent((to, navOpts) => navigate(absolutePath(to, router.base), navOpts)),\n  ];\n};\n\nconst useLocation = () => useLocationFromRouter(useRouter());\n\nconst useSearch = () => {\n  const router = useRouter();\n  return sanitizeSearch(router.searchHook(router));\n};\n\nconst matchRoute = (parser, route, path, loose) => {\n  // if the input is a regexp, skip parsing\n  const { pattern, keys } =\n    route instanceof RegExp\n      ? { keys: false, pattern: route }\n      : parser(route || \"*\", loose);\n\n  // array destructuring loses keys, so this is done in two steps\n  const result = pattern.exec(path) || [];\n\n  // when parser is in \"loose\" mode, `$base` is equal to the\n  // first part of the route that matches the pattern\n  // (e.g. for pattern `/a/:b` and path `/a/1/2/3` the `$base` is `a/1`)\n  // we use this for route nesting\n  const [$base, ...matches] = result;\n\n  return $base !== undefined\n    ? [\n        true,\n\n        (() => {\n          // for regex paths, `keys` will always be false\n\n          // an object with parameters matched, e.g. { foo: \"bar\" } for \"/:foo\"\n          // we \"zip\" two arrays here to construct the object\n          // [\"foo\"], [\"bar\"] → { foo: \"bar\" }\n          const groups =\n            keys !== false\n              ? Object.fromEntries(keys.map((key, i) => [key, matches[i]]))\n              : result.groups;\n\n          // convert the array to an instance of object\n          // this makes it easier to integrate with the existing param implementation\n          let obj = { ...matches };\n\n          // merge named capture groups with matches array\n          groups && Object.assign(obj, groups);\n\n          return obj;\n        })(),\n\n        // the third value if only present when parser is in \"loose\" mode,\n        // so that we can extract the base path for nested routes\n        ...(loose ? [$base] : []),\n      ]\n    : [false, null];\n};\n\nconst useRoute = (pattern) =>\n  matchRoute(useRouter().parser, pattern, useLocation()[0]);\n\n/*\n * Part 2, Low Carb Router API: Router, Route, Link, Switch\n */\n\nconst Router = ({ children, ...props }) => {\n  // the router we will inherit from - it is the closest router in the tree,\n  // unless the custom `hook` is provided (in that case it's the default one)\n  const parent_ = useRouter();\n  const parent = props.hook ? defaultRouter : parent_;\n\n  // holds to the context value: the router object\n  let value = parent;\n\n  // when `ssrPath` contains a `?` character, we can extract the search from it\n  const [path, search] = props.ssrPath?.split(\"?\") ?? [];\n  if (search) (props.ssrSearch = search), (props.ssrPath = path);\n\n  // hooks can define their own `href` formatter (e.g. for hash location)\n  props.hrefs = props.hrefs ?? props.hook?.hrefs;\n\n  // what is happening below: to avoid unnecessary rerenders in child components,\n  // we ensure that the router object reference is stable, unless there are any\n  // changes that require reload (e.g. `base` prop changes -> all components that\n  // get the router from the context should rerender, even if the component is memoized).\n  // the expected behaviour is:\n  //\n  //   1) when the resulted router is no different from the parent, use parent\n  //   2) if the custom `hook` prop is provided, we always inherit from the\n  //      default router instead. this resets all previously overridden options.\n  //   3) when the router is customized here, it should stay stable between renders\n  let ref = useRef({}),\n    prev = ref.current,\n    next = prev;\n\n  for (let k in parent) {\n    const option =\n      k === \"base\"\n        ? /* base is special case, it is appended to the parent's base */\n          parent[k] + (props[k] || \"\")\n        : props[k] || parent[k];\n\n    if (prev === next && option !== next[k]) {\n      ref.current = next = { ...next };\n    }\n\n    next[k] = option;\n\n    // the new router is no different from the parent or from the memoized value, use parent\n    if (option !== parent[k] || option !== value[k]) value = next;\n  }\n\n  return createElement(RouterCtx.Provider, { value, children });\n};\n\nconst h_route = ({ children, component }, params) => {\n  // React-Router style `component` prop\n  if (component) return createElement(component, { params });\n\n  // support render prop or plain children\n  return typeof children === \"function\" ? children(params) : children;\n};\n\n// Cache params object between renders if values are shallow equal\nconst useCachedParams = (value) => {\n  let prev = useRef(Params0);\n  const curr = prev.current;\n  return (prev.current =\n    // Update cache if number of params changed or any value changed\n    Object.keys(value).length !== Object.keys(curr).length ||\n    Object.entries(value).some(([k, v]) => v !== curr[k])\n      ? value // Return new value if there are changes\n      : curr); // Return cached value if nothing changed\n};\n\nfunction useSearchParams() {\n  const [location, navigate] = useLocation();\n\n  const search = useSearch();\n  const searchParams = useMemo(() => new URLSearchParams(search), [search]);\n\n  // cached value before next render, so you can call setSearchParams multiple times\n  let tempSearchParams = searchParams;\n\n  const setSearchParams = useEvent((nextInit, options) => {\n    tempSearchParams = new URLSearchParams(\n      typeof nextInit === \"function\" ? nextInit(tempSearchParams) : nextInit\n    );\n    navigate(location + \"?\" + tempSearchParams, options);\n  });\n\n  return [searchParams, setSearchParams];\n}\n\nconst Route = ({ path, nest, match, ...renderProps }) => {\n  const router = useRouter();\n  const [location] = useLocationFromRouter(router);\n\n  const [matches, routeParams, base] =\n    // `match` is a special prop to give up control to the parent,\n    // it is used by the `Switch` to avoid double matching\n    match ?? matchRoute(router.parser, path, location, nest);\n\n  // when `routeParams` is `null` (there was no match), the argument\n  // below becomes {...null} = {}, see the Object Spread specs\n  // https://tc39.es/proposal-object-rest-spread/#AbstractOperations-CopyDataProperties\n  const params = useCachedParams({ ...useParams(), ...routeParams });\n\n  if (!matches) return null;\n\n  const children = base\n    ? createElement(Router, { base }, h_route(renderProps, params))\n    : h_route(renderProps, params);\n\n  return createElement(ParamsCtx.Provider, { value: params, children });\n};\n\nconst Link = forwardRef((props, ref) => {\n  const router = useRouter();\n  const [currentPath, navigate] = useLocationFromRouter(router);\n\n  const {\n    to = \"\",\n    href: targetPath = to,\n    onClick: _onClick,\n    asChild,\n    children,\n    className: cls,\n    /* eslint-disable no-unused-vars */\n    replace /* ignore nav props */,\n    state /* ignore nav props */,\n    /* eslint-enable no-unused-vars */\n\n    ...restProps\n  } = props;\n\n  const onClick = useEvent((event) => {\n    // ignores the navigation when clicked using right mouse button or\n    // by holding a special modifier key: ctrl, command, win, alt, shift\n    if (\n      event.ctrlKey ||\n      event.metaKey ||\n      event.altKey ||\n      event.shiftKey ||\n      event.button !== 0\n    )\n      return;\n\n    _onClick?.(event);\n    if (!event.defaultPrevented) {\n      event.preventDefault();\n      navigate(targetPath, props);\n    }\n  });\n\n  // handle nested routers and absolute paths\n  const href = router.hrefs(\n    targetPath[0] === \"~\" ? targetPath.slice(1) : router.base + targetPath,\n    router // pass router as a second argument for convinience\n  );\n\n  return asChild && isValidElement(children)\n    ? cloneElement(children, { onClick, href })\n    : createElement(\"a\", {\n        ...restProps,\n        onClick,\n        href,\n        // `className` can be a function to apply the class if this link is active\n        className: cls?.call ? cls(currentPath === targetPath) : cls,\n        children,\n        ref,\n      });\n});\n\nconst flattenChildren = (children) =>\n  Array.isArray(children)\n    ? children.flatMap((c) =>\n        flattenChildren(c && c.type === Fragment ? c.props.children : c)\n      )\n    : [children];\n\nconst Switch = ({ children, location }) => {\n  const router = useRouter();\n  const [originalLocation] = useLocationFromRouter(router);\n\n  for (const element of flattenChildren(children)) {\n    let match = 0;\n\n    if (\n      isValidElement(element) &&\n      // we don't require an element to be of type Route,\n      // but we do require it to contain a truthy `path` prop.\n      // this allows to use different components that wrap Route\n      // inside of a switch, for example <AnimatedRoute />.\n      (match = matchRoute(\n        router.parser,\n        element.props.path,\n        location || originalLocation,\n        element.props.nest\n      ))[0]\n    )\n      return cloneElement(element, { match });\n  }\n\n  return null;\n};\n\nconst Redirect = (props) => {\n  const { to, href = to } = props;\n  const router = useRouter();\n  const [, navigate] = useLocationFromRouter(router);\n  const redirect = useEvent(() => navigate(to || href, props));\n  const { ssrContext } = router;\n\n  // redirect is guaranteed to be stable since it is returned from useEvent\n  useIsomorphicLayoutEffect(() => {\n    redirect();\n  }, []); // eslint-disable-line react-hooks/exhaustive-deps\n\n  if (ssrContext) {\n    ssrContext.redirectTo = to;\n  }\n\n  return null;\n};\n\nexport { Link, Redirect, Route, Router, Switch, matchRoute, useLocation, useParams, useRoute, useRouter, useSearch, useSearchParams };\n"], "mappings": ";;;;;;;;;AAAA;AAAA;AAAA;AAWA,KACG,WAAY;AACX,eAAS,GAAG,GAAG,GAAG;AAChB,eAAQ,MAAM,MAAM,MAAM,KAAK,IAAI,MAAM,IAAI,MAAQ,MAAM,KAAK,MAAM;AAAA,MACxE;AACA,eAAS,uBAAuB,WAAW,aAAa;AACtD,6BACE,WAAWA,OAAM,oBACf,oBAAoB,MACtB,QAAQ;AAAA,UACN;AAAA,QACF;AACF,YAAI,QAAQ,YAAY;AACxB,YAAI,CAAC,4BAA4B;AAC/B,cAAI,cAAc,YAAY;AAC9B,mBAAS,OAAO,WAAW,MACxB,QAAQ;AAAA,YACP;AAAA,UACF,GACC,6BAA6B;AAAA,QAClC;AACA,sBAAcC,UAAS;AAAA,UACrB,MAAM,EAAE,OAAc,YAAyB;AAAA,QACjD,CAAC;AACD,YAAI,OAAO,YAAY,CAAC,EAAE,MACxB,cAAc,YAAY,CAAC;AAC7B,QAAAC;AAAA,UACE,WAAY;AACV,iBAAK,QAAQ;AACb,iBAAK,cAAc;AACnB,mCAAuB,IAAI,KAAK,YAAY,EAAE,KAAW,CAAC;AAAA,UAC5D;AAAA,UACA,CAAC,WAAW,OAAO,WAAW;AAAA,QAChC;AACA,QAAAC;AAAA,UACE,WAAY;AACV,mCAAuB,IAAI,KAAK,YAAY,EAAE,KAAW,CAAC;AAC1D,mBAAO,UAAU,WAAY;AAC3B,qCAAuB,IAAI,KAAK,YAAY,EAAE,KAAW,CAAC;AAAA,YAC5D,CAAC;AAAA,UACH;AAAA,UACA,CAAC,SAAS;AAAA,QACZ;AACA,sBAAc,KAAK;AACnB,eAAO;AAAA,MACT;AACA,eAAS,uBAAuB,MAAM;AACpC,YAAI,oBAAoB,KAAK;AAC7B,eAAO,KAAK;AACZ,YAAI;AACF,cAAI,YAAY,kBAAkB;AAClC,iBAAO,CAAC,SAAS,MAAM,SAAS;AAAA,QAClC,SAAS,OAAO;AACd,iBAAO;AAAA,QACT;AAAA,MACF;AACA,eAAS,uBAAuB,WAAW,aAAa;AACtD,eAAO,YAAY;AAAA,MACrB;AACA,sBAAgB,OAAO,kCACrB,eACE,OAAO,+BAA+B,+BACxC,+BAA+B,4BAA4B,MAAM,CAAC;AACpE,UAAIH,SAAQ,iBACV,WAAW,eAAe,OAAO,OAAO,KAAK,OAAO,KAAK,IACzDC,YAAWD,OAAM,UACjBG,aAAYH,OAAM,WAClBE,mBAAkBF,OAAM,iBACxB,gBAAgBA,OAAM,eACtB,oBAAoB,OACpB,6BAA6B,OAC7B,OACE,gBAAgB,OAAO,UACvB,gBAAgB,OAAO,OAAO,YAC9B,gBAAgB,OAAO,OAAO,SAAS,gBACnC,yBACA;AACR,cAAQ,uBACN,WAAWA,OAAM,uBAAuBA,OAAM,uBAAuB;AACvE,sBAAgB,OAAO,kCACrB,eACE,OAAO,+BAA+B,8BACxC,+BAA+B,2BAA2B,MAAM,CAAC;AAAA,IACrE,GAAG;AAAA;AAAA;;;AC9FL;AAAA;AAAA;AAEA,QAAI,OAAuC;AACzC,aAAO,UAAU;AAAA,IACnB,OAAO;AACL,aAAO,UAAU;AAAA,IACnB;AAAA;AAAA;;;ACFO,SAAS,MAAM,OAAO,OAAO;AACnC,MAAI,iBAAiB,OAAQ,QAAO,EAAE,MAAK,OAAO,SAAQ,MAAM;AAChE,MAAI,GAAG,GAAG,KAAK,KAAK,OAAK,CAAC,GAAG,UAAQ,IAAI,MAAM,MAAM,MAAM,GAAG;AAC9D,MAAI,CAAC,KAAK,IAAI,MAAM;AAEpB,SAAO,MAAM,IAAI,MAAM,GAAG;AACzB,QAAI,IAAI,CAAC;AACT,QAAI,MAAM,KAAK;AACd,WAAK,KAAK,CAAC;AACX,iBAAW,IAAI,CAAC,MAAM,MAAM,eAAe;AAAA,IAC5C,WAAW,MAAM,KAAK;AACrB,UAAI,IAAI,QAAQ,KAAK,CAAC;AACtB,YAAM,IAAI,QAAQ,KAAK,CAAC;AACxB,WAAK,KAAM,IAAI,UAAU,GAAG,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,MAAM,MAAM,IAAI,MAAM,CAAE;AAClE,iBAAW,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,MAAM,mBAAmB;AAC9C,UAAI,CAAC,CAAC,CAAC,IAAK,aAAY,CAAC,CAAC,CAAC,IAAI,MAAM,MAAM,OAAO,IAAI,UAAU,GAAG;AAAA,IACpE,OAAO;AACN,iBAAW,MAAM;AAAA,IAClB;AAAA,EACD;AAEA,SAAO;AAAA,IACN;AAAA,IACA,SAAS,IAAI,OAAO,MAAM,WAAW,QAAQ,YAAa,QAAS,GAAG;AAAA,EACvE;AACD;;;AC7BA,YAAuB;AACvB,mBAAwI;AACxI,kBAAqC;AAIrC,IAAM,4BAAkC;AAIxC,IAAM,YAAY,CAAC,EACjB,OAAO,WAAW,eAClB,OAAO,OAAO,aAAa,eAC3B,OAAO,OAAO,SAAS,kBAAkB;AAQ3C,IAAM,4BAA4B,YACxB,wBACA;AAIV,IAAMI,sBACJ,6BAA6B;AAQ/B,IAAM,WAAW,CAAC,OAAO;AACvB,QAAM,MAAY,aAAO,CAAC,IAAI,IAAI,SAAS,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC,EAAE;AAI7D,EAAAA,oBAAmB,MAAM;AACvB,QAAI,CAAC,IAAI;AAAA,EACX,CAAC;AACD,SAAO,IAAI,CAAC;AACd;;;ACxCA,IAAM,gBAAgB;AACtB,IAAM,iBAAiB;AACvB,IAAM,oBAAoB;AAC1B,IAAM,kBAAkB;AACxB,IAAM,SAAS;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAEA,IAAM,6BAA6B,CAAC,aAAa;AAC/C,aAAW,SAAS,QAAQ;AAC1B,qBAAiB,OAAO,QAAQ;AAAA,EAClC;AACA,SAAO,MAAM;AACX,eAAW,SAAS,QAAQ;AAC1B,0BAAoB,OAAO,QAAQ;AAAA,IACrC;AAAA,EACF;AACF;AAEA,IAAM,sBAAsB,CAAC,IAAI,cAC/B,kCAAqB,4BAA4B,IAAI,KAAK;AAE5D,IAAM,gBAAgB,MAAM,SAAS;AAErC,IAAM,YAAY,CAAC,EAAE,YAAY,GAAG,IAAI,CAAC,MACvC,oBAAoB,eAAe,MAAM,SAAS;AAEpD,IAAM,kBAAkB,MAAM,SAAS;AAEvC,IAAM,cAAc,CAAC,EAAE,QAAQ,IAAI,CAAC,MAClC;AAAA,EACE;AAAA,EACA,UAAU,MAAM,UAAU;AAC5B;AAMF,IAAM,WAAW,CAAC,IAAI,EAAE,UAAU,OAAO,QAAQ,KAAK,IAAI,CAAC,MACzD,QAAQ,UAAU,oBAAoB,cAAc,EAAE,OAAO,IAAI,EAAE;AAIrE,IAAM,qBAAqB,CAAC,OAAO,CAAC,MAAM,CAAC,YAAY,IAAI,GAAG,QAAQ;AAEtE,IAAM,WAAW,OAAO,IAAI,WAAW;AAOvC,IAAI,OAAO,YAAY,eAAe,OAAO,OAAO,QAAQ,MAAM,aAAa;AAC7E,aAAW,QAAQ,CAAC,gBAAgB,iBAAiB,GAAG;AACtD,UAAM,WAAW,QAAQ,IAAI;AAI7B,YAAQ,IAAI,IAAI,WAAY;AAC1B,YAAM,SAAS,SAAS,MAAM,MAAM,SAAS;AAC7C,YAAM,QAAQ,IAAI,MAAM,IAAI;AAC5B,YAAM,YAAY;AAElB,oBAAc,KAAK;AACnB,aAAO;AAAA,IACT;AAAA,EACF;AAIA,SAAO,eAAe,QAAQ,UAAU,EAAE,OAAO,KAAK,CAAC;AACzD;;;ACxEA,IAAM,gBAAgB,CAAC,MAAM,SAC3B,CAAC,KAAK,YAAY,EAAE,QAAQ,KAAK,YAAY,CAAC,IAC1C,KAAK,MAAM,KAAK,MAAM,KAAK,MAC3B,MAAM;AAKZ,IAAM,eAAe,CAAC,OAAO,OAAQ,SAAS,MAAM,KAAK;AAEzD,IAAM,eAAe,CAAC,IAAI,SACxB,GAAG,CAAC,MAAM,MAAM,GAAG,MAAM,CAAC,IAAI,aAAa,IAAI,IAAI;AAErD,IAAM,eAAe,CAAC,OAAO,IAAI,SAC/B,cAAc,SAAS,aAAa,IAAI,CAAC,GAAG,SAAS,IAAI,CAAC;AAK5D,IAAM,UAAU,CAAC,QAAS,IAAI,CAAC,MAAM,MAAM,IAAI,MAAM,CAAC,IAAI;AAK1D,IAAM,WAAW,CAAC,QAAQ;AACxB,MAAI;AACF,WAAO,UAAU,GAAG;AAAA,EACtB,SAAS,IAAI;AAEX,WAAO;AAAA,EACT;AACF;AAEA,IAAM,iBAAiB,CAAC,WAAW,SAAS,QAAQ,MAAM,CAAC;AAU3D,IAAM,gBAAgB;AAAA,EACpB,MAAM;AAAA,EACN,YAAY;AAAA,EACZ,QAAQ;AAAA,EACR,MAAM;AAAA;AAAA,EAEN,SAAS;AAAA,EACT,WAAW;AAAA;AAAA,EAEX,YAAY;AAAA;AAAA,EAEZ,OAAO,CAAC,MAAM;AAChB;AAEA,IAAM,gBAAY,4BAAc,aAAa;AAG7C,IAAM,YAAY,UAAM,yBAAW,SAAS;AAO5C,IAAM,UAAU,CAAC;AAAjB,IACE,gBAAY,4BAAc,OAAO;AAEnC,IAAM,YAAY,UAAM,yBAAW,SAAS;AAQ5C,IAAM,wBAAwB,CAAC,WAAW;AACxC,QAAM,CAACC,WAAUC,SAAQ,IAAI,OAAO,KAAK,MAAM;AAK/C,SAAO;AAAA,IACL,aAAa,OAAO,MAAMD,SAAQ;AAAA,IAClC,SAAS,CAAC,IAAI,YAAYC,UAAS,aAAa,IAAI,OAAO,IAAI,GAAG,OAAO,CAAC;AAAA,EAC5E;AACF;AAEA,IAAM,cAAc,MAAM,sBAAsB,UAAU,CAAC;AAE3D,IAAMC,aAAY,MAAM;AACtB,QAAM,SAAS,UAAU;AACzB,SAAO,eAAe,OAAO,WAAW,MAAM,CAAC;AACjD;AAEA,IAAM,aAAa,CAAC,QAAQ,OAAO,MAAM,UAAU;AAEjD,QAAM,EAAE,SAAS,KAAK,IACpB,iBAAiB,SACb,EAAE,MAAM,OAAO,SAAS,MAAM,IAC9B,OAAO,SAAS,KAAK,KAAK;AAGhC,QAAM,SAAS,QAAQ,KAAK,IAAI,KAAK,CAAC;AAMtC,QAAM,CAAC,OAAO,GAAG,OAAO,IAAI;AAE5B,SAAO,UAAU,SACb;AAAA,IACE;AAAA,KAEC,MAAM;AAML,YAAM,SACJ,SAAS,QACL,OAAO,YAAY,KAAK,IAAI,CAAC,KAAK,MAAM,CAAC,KAAK,QAAQ,CAAC,CAAC,CAAC,CAAC,IAC1D,OAAO;AAIb,UAAI,MAAM,EAAE,GAAG,QAAQ;AAGvB,gBAAU,OAAO,OAAO,KAAK,MAAM;AAEnC,aAAO;AAAA,IACT,GAAG;AAAA;AAAA;AAAA,IAIH,GAAI,QAAQ,CAAC,KAAK,IAAI,CAAC;AAAA,EACzB,IACA,CAAC,OAAO,IAAI;AAClB;AAEA,IAAM,WAAW,CAAC,YAChB,WAAW,UAAU,EAAE,QAAQ,SAAS,YAAY,EAAE,CAAC,CAAC;AAM1D,IAAM,SAAS,CAAC,EAAE,UAAU,GAAG,MAAM,MAAM;AAhK3C;AAmKE,QAAM,UAAU,UAAU;AAC1B,QAAM,SAAS,MAAM,OAAO,gBAAgB;AAG5C,MAAI,QAAQ;AAGZ,QAAM,CAAC,MAAM,MAAM,MAAI,WAAM,YAAN,mBAAe,MAAM,SAAQ,CAAC;AACrD,MAAI,OAAQ,CAAC,MAAM,YAAY,QAAU,MAAM,UAAU;AAGzD,QAAM,QAAQ,MAAM,WAAS,WAAM,SAAN,mBAAY;AAYzC,MAAI,UAAM,qBAAO,CAAC,CAAC,GACjB,OAAO,IAAI,SACX,OAAO;AAET,WAAS,KAAK,QAAQ;AACpB,UAAM,SACJ,MAAM;AAAA;AAAA,MAEF,OAAO,CAAC,KAAK,MAAM,CAAC,KAAK;AAAA,QACzB,MAAM,CAAC,KAAK,OAAO,CAAC;AAE1B,QAAI,SAAS,QAAQ,WAAW,KAAK,CAAC,GAAG;AACvC,UAAI,UAAU,OAAO,EAAE,GAAG,KAAK;AAAA,IACjC;AAEA,SAAK,CAAC,IAAI;AAGV,QAAI,WAAW,OAAO,CAAC,KAAK,WAAW,MAAM,CAAC,EAAG,SAAQ;AAAA,EAC3D;AAEA,aAAO,4BAAc,UAAU,UAAU,EAAE,OAAO,SAAS,CAAC;AAC9D;AAEA,IAAM,UAAU,CAAC,EAAE,UAAU,UAAU,GAAG,WAAW;AAEnD,MAAI,UAAW,YAAO,4BAAc,WAAW,EAAE,OAAO,CAAC;AAGzD,SAAO,OAAO,aAAa,aAAa,SAAS,MAAM,IAAI;AAC7D;AAGA,IAAM,kBAAkB,CAAC,UAAU;AACjC,MAAI,WAAO,qBAAO,OAAO;AACzB,QAAM,OAAO,KAAK;AAClB,SAAQ,KAAK;AAAA,EAEX,OAAO,KAAK,KAAK,EAAE,WAAW,OAAO,KAAK,IAAI,EAAE,UAChD,OAAO,QAAQ,KAAK,EAAE,KAAK,CAAC,CAAC,GAAG,CAAC,MAAM,MAAM,KAAK,CAAC,CAAC,IAChD,QACA;AACR;AAEA,SAAS,kBAAkB;AACzB,QAAM,CAACF,WAAUC,SAAQ,IAAI,YAAY;AAEzC,QAAM,SAASC,WAAU;AACzB,QAAM,mBAAe,sBAAQ,MAAM,IAAI,gBAAgB,MAAM,GAAG,CAAC,MAAM,CAAC;AAGxE,MAAI,mBAAmB;AAEvB,QAAM,kBAAkB,SAAS,CAAC,UAAU,YAAY;AACtD,uBAAmB,IAAI;AAAA,MACrB,OAAO,aAAa,aAAa,SAAS,gBAAgB,IAAI;AAAA,IAChE;AACA,IAAAD,UAASD,YAAW,MAAM,kBAAkB,OAAO;AAAA,EACrD,CAAC;AAED,SAAO,CAAC,cAAc,eAAe;AACvC;AAEA,IAAM,QAAQ,CAAC,EAAE,MAAM,MAAM,OAAO,GAAG,YAAY,MAAM;AACvD,QAAM,SAAS,UAAU;AACzB,QAAM,CAACA,SAAQ,IAAI,sBAAsB,MAAM;AAE/C,QAAM,CAAC,SAAS,aAAa,IAAI;AAAA;AAAA;AAAA,IAG/B,SAAS,WAAW,OAAO,QAAQ,MAAMA,WAAU,IAAI;AAAA;AAKzD,QAAM,SAAS,gBAAgB,EAAE,GAAG,UAAU,GAAG,GAAG,YAAY,CAAC;AAEjE,MAAI,CAAC,QAAS,QAAO;AAErB,QAAM,WAAW,WACb,4BAAc,QAAQ,EAAE,KAAK,GAAG,QAAQ,aAAa,MAAM,CAAC,IAC5D,QAAQ,aAAa,MAAM;AAE/B,aAAO,4BAAc,UAAU,UAAU,EAAE,OAAO,QAAQ,SAAS,CAAC;AACtE;AAEA,IAAM,WAAO,yBAAW,CAAC,OAAO,QAAQ;AACtC,QAAM,SAAS,UAAU;AACzB,QAAM,CAAC,aAAaC,SAAQ,IAAI,sBAAsB,MAAM;AAE5D,QAAM;AAAA,IACJ,KAAK;AAAA,IACL,MAAM,aAAa;AAAA,IACnB,SAAS;AAAA,IACT;AAAA,IACA;AAAA,IACA,WAAW;AAAA;AAAA,IAEX;AAAA,IACA;AAAA;AAAA,IAGA,GAAG;AAAA,EACL,IAAI;AAEJ,QAAM,UAAU,SAAS,CAAC,UAAU;AAGlC,QACE,MAAM,WACN,MAAM,WACN,MAAM,UACN,MAAM,YACN,MAAM,WAAW;AAEjB;AAEF,yCAAW;AACX,QAAI,CAAC,MAAM,kBAAkB;AAC3B,YAAM,eAAe;AACrB,MAAAA,UAAS,YAAY,KAAK;AAAA,IAC5B;AAAA,EACF,CAAC;AAGD,QAAM,OAAO,OAAO;AAAA,IAClB,WAAW,CAAC,MAAM,MAAM,WAAW,MAAM,CAAC,IAAI,OAAO,OAAO;AAAA,IAC5D;AAAA;AAAA,EACF;AAEA,SAAO,eAAW,6BAAe,QAAQ,QACrC,2BAAa,UAAU,EAAE,SAAS,KAAK,CAAC,QACxC,4BAAc,KAAK;AAAA,IACjB,GAAG;AAAA,IACH;AAAA,IACA;AAAA;AAAA,IAEA,YAAW,2BAAK,QAAO,IAAI,gBAAgB,UAAU,IAAI;AAAA,IACzD;AAAA,IACA;AAAA,EACF,CAAC;AACP,CAAC;AAED,IAAM,kBAAkB,CAAC,aACvB,MAAM,QAAQ,QAAQ,IAClB,SAAS;AAAA,EAAQ,CAAC,MAChB,gBAAgB,KAAK,EAAE,SAAS,wBAAW,EAAE,MAAM,WAAW,CAAC;AACjE,IACA,CAAC,QAAQ;AAEf,IAAM,SAAS,CAAC,EAAE,UAAU,UAAAD,UAAS,MAAM;AACzC,QAAM,SAAS,UAAU;AACzB,QAAM,CAAC,gBAAgB,IAAI,sBAAsB,MAAM;AAEvD,aAAW,WAAW,gBAAgB,QAAQ,GAAG;AAC/C,QAAI,QAAQ;AAEZ,YACE,6BAAe,OAAO;AAAA;AAAA;AAAA;AAAA,KAKrB,QAAQ;AAAA,MACP,OAAO;AAAA,MACP,QAAQ,MAAM;AAAA,MACdA,aAAY;AAAA,MACZ,QAAQ,MAAM;AAAA,IAChB,GAAG,CAAC;AAEJ,iBAAO,2BAAa,SAAS,EAAE,MAAM,CAAC;AAAA,EAC1C;AAEA,SAAO;AACT;AAEA,IAAM,WAAW,CAAC,UAAU;AAC1B,QAAM,EAAE,IAAI,OAAO,GAAG,IAAI;AAC1B,QAAM,SAAS,UAAU;AACzB,QAAM,CAAC,EAAEC,SAAQ,IAAI,sBAAsB,MAAM;AACjD,QAAM,WAAW,SAAS,MAAMA,UAAS,MAAM,MAAM,KAAK,CAAC;AAC3D,QAAM,EAAE,WAAW,IAAI;AAGvB,4BAA0B,MAAM;AAC9B,aAAS;AAAA,EACX,GAAG,CAAC,CAAC;AAEL,MAAI,YAAY;AACd,eAAW,aAAa;AAAA,EAC1B;AAEA,SAAO;AACT;", "names": ["React", "useState", "useLayoutEffect", "useEffect", "useInsertionEffect", "location", "navigate", "useSearch"]}