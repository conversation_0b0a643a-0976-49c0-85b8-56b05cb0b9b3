#!/usr/bin/env tsx

import { storage } from "../server/storage";

async function clearProblematicSyncStatuses() {
  console.log("🧹 Clearing problematic sync statuses...");
  
  try {
    // Get all sync statuses
    const syncStatuses = await storage.listSyncStatuses();
    console.log(`Found ${syncStatuses.length} sync statuses`);
    
    // Clear test/demo sync statuses that are causing issues
    const problematicLocationIds = [1, 2, 99999];
    
    for (const locationId of problematicLocationIds) {
      console.log(`Clearing sync statuses for location ${locationId}...`);
      
      // Disable sync statuses for this location
      const locationSyncs = syncStatuses.filter(s => s.locationId === locationId);
      
      for (const sync of locationSyncs) {
        await storage.updateSyncStatus(sync.locationId, sync.platform, {
          isEnabled: 0,
          lastSyncStatus: 'disabled',
          lastSyncError: 'Disabled - test data cleanup',
          updatedAt: Date.now(),
        });
        
        console.log(`  ✅ Disabled sync for location ${sync.locationId} (${sync.platform})`);
      }
    }
    
    console.log("✅ Cleanup completed successfully!");
    console.log("You can now re-enable the sync service in server/routes.ts");
    
  } catch (error) {
    console.error("❌ Error during cleanup:", error);
    process.exit(1);
  }
}

// Run the cleanup
clearProblematicSyncStatuses().then(() => {
  process.exit(0);
});
