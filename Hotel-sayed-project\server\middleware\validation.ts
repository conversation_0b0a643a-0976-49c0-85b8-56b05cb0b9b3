import { Request, Response, NextFunction } from 'express';
import { z } from 'zod';
import DOMPurify from 'isomorphic-dompurify';

// Input sanitization
export function sanitizeInput(input: string): string {
  if (typeof input !== 'string') return '';
  
  // Remove HTML tags and potentially dangerous content
  const sanitized = DOMPurify.sanitize(input, { 
    ALLOWED_TAGS: [],
    ALLOWED_ATTR: []
  });
  
  // Trim whitespace and limit length
  return sanitized.trim().substring(0, 10000);
}

// Validation schemas
export const googleSyncSchema = z.object({
  locationName: z.string()
    .min(1, 'Location name is required')
    .max(500, 'Location name too long')
    .regex(/^[a-zA-Z0-9\s\-_\/]+$/, 'Invalid location name format'),
});

export const replySchema = z.object({
  replyText: z.string()
    .min(1, 'Reply text is required')
    .max(4000, 'Reply text too long')
    .refine(text => text.trim().length > 0, 'Reply cannot be empty'),
});

export const reviewFilterSchema = z.object({
  platform: z.enum(['all', 'google', 'booking', 'airbnb', 'tripadvisor']).optional(),
  replyStatus: z.enum(['all', 'replied', 'not-replied']).optional(),
  sortBy: z.enum(['date-desc', 'date-asc', 'rating-desc', 'rating-asc']).optional(),
  search: z.string().max(200).optional(),
  page: z.number().min(1).max(1000).optional(),
  limit: z.number().min(1).max(100).optional(),
});

// Validation middleware factory
export function validateBody<T>(schema: z.ZodSchema<T>) {
  return (req: Request, res: Response, next: NextFunction): void => {
    try {
      // Sanitize string inputs
      if (req.body && typeof req.body === 'object') {
        for (const [key, value] of Object.entries(req.body)) {
          if (typeof value === 'string') {
            req.body[key] = sanitizeInput(value);
          }
        }
      }

      // Validate against schema
      const result = schema.safeParse(req.body);

      if (!result.success) {
        res.status(400).json({
          message: 'Validation error',
          errors: result.error.errors.map(err => ({
            field: err.path.join('.'),
            message: err.message
          }))
        });
        return;
      }

      req.body = result.data;
      next();
    } catch (error) {
      console.error('Validation middleware error:', error);
      res.status(500).json({ message: 'Internal validation error' });
    }
  };
}

// Query parameter validation
export function validateQuery<T>(schema: z.ZodSchema<T>) {
  return (req: Request, res: Response, next: NextFunction): void => {
    try {
      // Convert string numbers to actual numbers for validation
      const query: any = { ...req.query };
      for (const [key, value] of Object.entries(query)) {
        if (typeof value === 'string' && !isNaN(Number(value))) {
          query[key] = Number(value);
        }
      }

      const result = schema.safeParse(query);

      if (!result.success) {
        res.status(400).json({
          message: 'Query validation error',
          errors: result.error.errors.map(err => ({
            field: err.path.join('.'),
            message: err.message
          }))
        });
        return;
      }

      req.query = result.data as any;
      next();
    } catch (error) {
      console.error('Query validation middleware error:', error);
      res.status(500).json({ message: 'Internal validation error' });
    }
  };
}

// Security headers for API responses
export function secureApiResponse(req: Request, res: Response, next: NextFunction): void {
  // Prevent caching of sensitive data
  res.set({
    'Cache-Control': 'no-store, no-cache, must-revalidate, proxy-revalidate',
    'Pragma': 'no-cache',
    'Expires': '0',
    'Surrogate-Control': 'no-store',
    'X-Content-Type-Options': 'nosniff',
    'X-Frame-Options': 'DENY',
    'X-XSS-Protection': '1; mode=block',
  });

  next();
}

// Request logging for security monitoring
export function securityLogger(req: Request, res: Response, next: NextFunction): void {
  const startTime = Date.now();

  // Skip security logging for development Vite requests
  const isDevelopment = process.env.NODE_ENV === 'development';
  const isViteRequest = req.url.includes('/@vite/') ||
                       req.url.includes('/@fs/') ||
                       req.url.includes('/@react-refresh') ||
                       req.url.includes('.js?v=') ||
                       req.url.includes('.ts?v=') ||
                       req.url.includes('.tsx?v=') ||
                       req.url.includes('.css?v=');

  if (isDevelopment && isViteRequest) {
    next();
    return;
  }

  // Log suspicious patterns (only for non-development or non-Vite requests)
  const suspiciousPatterns = [
    /script/i,
    /javascript/i,
    /vbscript/i,
    /onload/i,
    /onerror/i,
    /<.*>/,
    /union.*select/i,
    /drop.*table/i,
  ];

  const requestData = JSON.stringify({
    url: req.url,
    body: req.body,
    query: req.query,
    headers: req.headers
  });

  const hasSuspiciousContent = suspiciousPatterns.some(pattern =>
    pattern.test(requestData)
  );

  if (hasSuspiciousContent && !isDevelopment) {
    console.warn('🚨 Suspicious request detected:', {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      url: req.url,
      method: req.method,
      timestamp: new Date().toISOString()
    });
  }
  
  res.on('finish', () => {
    const duration = Date.now() - startTime;
    
    // Log slow requests
    if (duration > 5000) {
      console.warn('🐌 Slow request detected:', {
        url: req.url,
        method: req.method,
        duration: `${duration}ms`,
        statusCode: res.statusCode
      });
    }
  });
  
  next();
}

// Error handling middleware
export function errorHandler(error: Error, req: Request, res: Response, next: NextFunction) {
  console.error('API Error:', {
    error: error.message,
    stack: error.stack,
    url: req.url,
    method: req.method,
    ip: req.ip,
    timestamp: new Date().toISOString()
  });
  
  // Don't leak error details in production
  const isDevelopment = process.env.NODE_ENV === 'development';
  
  res.status(500).json({
    message: 'Internal server error',
    ...(isDevelopment && { error: error.message, stack: error.stack })
  });
}
