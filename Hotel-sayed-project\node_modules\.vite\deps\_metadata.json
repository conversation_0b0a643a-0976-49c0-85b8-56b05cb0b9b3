{"hash": "0f9907cf", "configHash": "b16f8098", "lockfileHash": "86e77510", "browserHash": "6711d559", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "cadc1b76", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "3adc4047", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "50c034e3", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "47e1cd6f", "needsInterop": true}, "@hookform/resolvers/zod": {"src": "../../@hookform/resolvers/zod/dist/zod.mjs", "file": "@hookform_resolvers_zod.js", "fileHash": "60b67c4a", "needsInterop": false}, "@radix-ui/react-dialog": {"src": "../../@radix-ui/react-dialog/dist/index.mjs", "file": "@radix-ui_react-dialog.js", "fileHash": "e10d357c", "needsInterop": false}, "@radix-ui/react-dropdown-menu": {"src": "../../@radix-ui/react-dropdown-menu/dist/index.mjs", "file": "@radix-ui_react-dropdown-menu.js", "fileHash": "3c0b1bff", "needsInterop": false}, "@radix-ui/react-label": {"src": "../../@radix-ui/react-label/dist/index.mjs", "file": "@radix-ui_react-label.js", "fileHash": "24b630db", "needsInterop": false}, "@radix-ui/react-popover": {"src": "../../@radix-ui/react-popover/dist/index.mjs", "file": "@radix-ui_react-popover.js", "fileHash": "702e433a", "needsInterop": false}, "@radix-ui/react-separator": {"src": "../../@radix-ui/react-separator/dist/index.mjs", "file": "@radix-ui_react-separator.js", "fileHash": "b73273ff", "needsInterop": false}, "@radix-ui/react-slot": {"src": "../../@radix-ui/react-slot/dist/index.mjs", "file": "@radix-ui_react-slot.js", "fileHash": "5629b729", "needsInterop": false}, "@radix-ui/react-switch": {"src": "../../@radix-ui/react-switch/dist/index.mjs", "file": "@radix-ui_react-switch.js", "fileHash": "869a82a3", "needsInterop": false}, "@radix-ui/react-tabs": {"src": "../../@radix-ui/react-tabs/dist/index.mjs", "file": "@radix-ui_react-tabs.js", "fileHash": "99671587", "needsInterop": false}, "@radix-ui/react-toast": {"src": "../../@radix-ui/react-toast/dist/index.mjs", "file": "@radix-ui_react-toast.js", "fileHash": "910ae633", "needsInterop": false}, "@tanstack/react-query": {"src": "../../@tanstack/react-query/build/modern/index.js", "file": "@tanstack_react-query.js", "fileHash": "564cc6ae", "needsInterop": false}, "class-variance-authority": {"src": "../../class-variance-authority/dist/index.mjs", "file": "class-variance-authority.js", "fileHash": "293ffa20", "needsInterop": false}, "clsx": {"src": "../../clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "51b9f5be", "needsInterop": false}, "date-fns": {"src": "../../date-fns/index.js", "file": "date-fns.js", "fileHash": "cf586d18", "needsInterop": false}, "jspdf": {"src": "../../jspdf/dist/jspdf.es.min.js", "file": "jspdf.js", "fileHash": "cc3cb6bc", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "9b351f1a", "needsInterop": false}, "react-day-picker": {"src": "../../react-day-picker/dist/esm/index.js", "file": "react-day-picker.js", "fileHash": "2f274e4b", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "90d2ead3", "needsInterop": true}, "react-hook-form": {"src": "../../react-hook-form/dist/index.esm.mjs", "file": "react-hook-form.js", "fileHash": "0af6f259", "needsInterop": false}, "react-icons/fa": {"src": "../../react-icons/fa/index.mjs", "file": "react-icons_fa.js", "fileHash": "273953e8", "needsInterop": false}, "recharts": {"src": "../../recharts/es6/index.js", "file": "recharts.js", "fileHash": "57e57d8d", "needsInterop": false}, "socket.io-client": {"src": "../../socket.io-client/build/esm/index.js", "file": "socket__io-client.js", "fileHash": "a32a5922", "needsInterop": false}, "tailwind-merge": {"src": "../../tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "d087ff8f", "needsInterop": false}, "wouter": {"src": "../../wouter/esm/index.js", "file": "wouter.js", "fileHash": "8f767419", "needsInterop": false}, "zod": {"src": "../../zod/dist/esm/index.js", "file": "zod.js", "fileHash": "5e74a3f1", "needsInterop": false}}, "chunks": {"html2canvas.esm-H3DJHMJZ": {"file": "html2canvas__esm-H3DJHMJZ.js"}, "purify.es-FJVICIXJ": {"file": "purify__es-FJVICIXJ.js"}, "index.es-CARAXN67": {"file": "index__es-CARAXN67.js"}, "chunk-OFUI4ZNC": {"file": "chunk-OFUI4ZNC.js"}, "chunk-D7ZASVPN": {"file": "chunk-D7ZASVPN.js"}, "chunk-U7P2NEEE": {"file": "chunk-U7P2NEEE.js"}, "chunk-7FKF5WM7": {"file": "chunk-7FKF5WM7.js"}, "chunk-IBZWOJCV": {"file": "chunk-IBZWOJCV.js"}, "chunk-M7SMWTMQ": {"file": "chunk-M7SMWTMQ.js"}, "chunk-V7RLCCQP": {"file": "chunk-V7RLCCQP.js"}, "chunk-V765WWC3": {"file": "chunk-V765WWC3.js"}, "chunk-YDUX3ZJU": {"file": "chunk-YDUX3ZJU.js"}, "chunk-CM2GFZJK": {"file": "chunk-CM2GFZJK.js"}, "chunk-QO5YCUJT": {"file": "chunk-QO5YCUJT.js"}, "chunk-5XBRDLLH": {"file": "chunk-5XBRDLLH.js"}, "chunk-RJ7WAUOI": {"file": "chunk-RJ7WAUOI.js"}, "chunk-J2KA7NLM": {"file": "chunk-J2KA7NLM.js"}, "chunk-E7TSFT4J": {"file": "chunk-E7TSFT4J.js"}, "chunk-RPCDYKBN": {"file": "chunk-RPCDYKBN.js"}, "chunk-KBTYAULA": {"file": "chunk-KBTYAULA.js"}, "chunk-QCHXOAYK": {"file": "chunk-QCHXOAYK.js"}, "chunk-WOOG5QLI": {"file": "chunk-WOOG5QLI.js"}}}